import { _Satuan, type Satuan } from '$lib/schema/general';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;
	body: Satuan;
	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();
	body = $state<Satuan>(_Satuan);
	mode = $state<'add' | 'view' | 'edit'>('add');

	addNewBody() {
		this.body = _Satuan;
	}
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
