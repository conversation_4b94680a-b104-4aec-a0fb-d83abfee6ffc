import type { Handle, HandleFetch } from '@sveltejs/kit';

import { env } from '$env/dynamic/private';

export const handle: Handle = async ({ event, resolve }) => {
	//TEMPORARY
	event.locals.auth = {
		id: 'K-0001',
		nama: '<PERSON>'
	};

	return await resolve(event);
};

export const handleFetch: HandleFetch = async ({ fetch, request }) => {
	if (request.url.startsWith(env.API_HOST + ':' + env.API_PORT)) {
		if (!request.headers.get('Content-Type'))
			request.headers.set('Content-Type', 'application/json; charset=utf-8');
		request.headers.set('Accept', '*/*');
	}

	return fetch(request);
};
