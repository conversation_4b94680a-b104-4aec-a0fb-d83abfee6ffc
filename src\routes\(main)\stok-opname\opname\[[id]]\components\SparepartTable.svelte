<script lang="ts">
	import Table from '$lib/table/Table.svelte';

	import { getOpnameState } from './OpnameState.svelte';
	import { getAllSparepart } from '$lib/remote/sparepart.remote';
	import { _KartuStok, _StokOpname, type DetailOpname } from '$lib/schema/general';
	import { page } from '$app/state';
	import { getOpnameDetail } from '$lib/remote/opname.remote';

	const opnameState = getOpnameState();
	opnameState.sparepart = page.params.id
		? await getOpnameDetail(page.params.id)
		: ((await getAllSparepart()).data.map((item) => ({
				stock_opname: _StokOpname,
				kartustok: { ..._KartuStok, sparepart: item },
				stok_sistem: item.stok_barang,
				stok_fisik: item.stok_barang,
				selisih: 0,
				keterangan: '',
				created_at: '',
				updated_at: null,
				deleted_at: null
			})) as DetailOpname[]);
</script>

<Table
	table_header={[
		['numbering', 'No.'],
		['custom', '<PERSON>a Sparepart'],
		['stok_sistem', 'Stok Sistem'],
		['custom', 'Stok Fisik'],
		['custom', 'Selisih'],
		['custom', 'Keterangan']
	]}
	table_data={opnameState.filtered}
>
	{#snippet custom({ header, body })}
		{#if header === 'Nama Sparepart'}
			{body.kartustok.sparepart.nama_sparepart}
		{:else if header === 'Stok Fisik'}
			<input type="number" class="input input-sm w-16" min={0} bind:value={body.stok_fisik} />
		{:else if header === 'Selisih'}
			{body.stok_fisik - body.stok_sistem}
		{:else if header === 'Keterangan'}
			<input
				type="text"
				class="input input-sm w-full"
				placeholder="keterangan perubahan stok"
				bind:value={body.keterangan}
			/>
		{/if}
	{/snippet}
</Table>
