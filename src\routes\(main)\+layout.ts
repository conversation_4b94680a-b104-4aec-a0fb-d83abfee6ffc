import type { LayoutLoad } from './$types';

const ROUTING_GROUP: { route: string; headerTitle: string; routeGroup: string }[] = [
	{
		route: '/(main)/montir/lokasi',
		headerTitle: 'Lokasi Montir',
		routeGroup: 'montir/lokasi'
	},

	{
		route: '/(main)/kendaraan/riwayat/[no_polisi]',
		headerTitle: 'Riwayat Kendaraan',
		routeGroup: 'kendaraan'
	},

	{
		route: '(main)/order/data/[[order_id]]',
		headerTitle: 'Data Order',
		routeGroup: 'order'
	}
] as const;

export const load: LayoutLoad = async ({ url, route }) => {
	const headerTitle =
		ROUTING_GROUP.find((item) => item.route.startsWith(route.id))?.headerTitle ??
		url.pathname.toUpperCase().replace('/', ' ');

	const routeGroup =
		ROUTING_GROUP.find((item) => item.route.startsWith(route.id))?.routeGroup ??
		url.pathname.split('/')[1];

	return {
		headerTitle,
		routeGroup
	};
};
