import type { Invoice, Order } from '$lib/schema/order';

import { form, query } from '$app/server';
import { Effect, Schema } from 'effect';

import { effectfulFetch } from '$lib/utils/fetch';
import { orderStatus } from '$lib/schema/literal';

const OrderTypeSchema = Schema.Literal(...orderStatus).pipe(Schema.standardSchemaV1);
export const getOrder = query(OrderTypeSchema, async (orderStatus) => {
	let link =
		orderStatus === 'Selesai' || orderStatus === 'Void'
			? '/invoice'
			: `/order/status/${orderStatus}`;

	const getOrder = effectfulFetch<Order[] | Invoice[]>(link);
	const response = await Effect.runPromise(getOrder);

	if (response.kind === 'success') {
		if (orderStatus === 'Selesai' || orderStatus === 'Void') {
			const filteredByVoid = (response.data as Invoice[]).filter((item) =>
				orderStatus === 'Selesai' ? !item.void_status : item.void_status
			);
			const orderData =
				filteredByVoid.map(
					(item) =>
						({
							...item.order,
							nomor_invoice: item.nomor_invoice,
							status: orderStatus === 'Void' ? 'Void' : item.order.status
						}) as Order
				) ?? [];

			return orderData;
		} else return response.data as Order[];
	} else return [];
});

export const assignOrder = form(async (data) => {
	const order: Order = JSON.parse(data.get('order') as string);

	const action = effectfulFetch(`/order`, {
		method: 'PUT',
		body: JSON.stringify({
			...order,
			status_order: 'Dikerjakan',
			id_bengkel: order.bengkel.id_bengkel,
			id_karyawan: order.karyawan.id_karyawan,
			id_customer: order.customer.id_customer,
			nomor_polisi: order.kendaraan.nomor_polisi
		})
	});

	const response = await Effect.runPromise(action);

	return response;
});

export const deleteOrder = form(async (data) => {
	const nomor_order = data.get('nomor_order') as string;

	const action = effectfulFetch(`/order/${nomor_order}`, {
		method: 'DELETE'
	});

	const response = await Effect.runPromise(action);

	return response;
});

export const voidInvoice = form(async (data) => {
	const nomor_invoice = data.get('nomor_invoice') as string;

	const action = effectfulFetch(`/invoice`, {
		method: 'DELETE',
		body: JSON.stringify({ nomor_invoice, keterangan_void: '' })
	});

	const response = await Effect.runPromise(action);
	return response;
});
