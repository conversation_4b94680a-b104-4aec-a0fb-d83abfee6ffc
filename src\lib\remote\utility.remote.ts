import { command } from '$app/server';
import { Schema } from 'effect';
import { webkit } from 'playwright';

const PdfExportOptionsSchema = Schema.Struct({
	route: Schema.String,
	filename: Schema.optional(Schema.String),
	options: Schema.optional(
		Schema.Struct({
			format: Schema.optional(Schema.Literal('A4', 'A3', 'A5', 'Letter', 'Legal')),
			landscape: Schema.optional(Schema.Boolean),
			margin: Schema.optional(
				Schema.Struct({
					top: Schema.optional(Schema.String),
					right: Schema.optional(Schema.String),
					bottom: Schema.optional(Schema.String),
					left: Schema.optional(Schema.String)
				})
			),
			printBackground: Schema.optional(Schema.Boolean),
			scale: Schema.optional(Schema.Number)
		})
	)
}).pipe(Schema.standardSchemaV1);

export const exportAsPdf = command(PdfExportOptionsSchema, async ({ route, filename, options }) => {
	let browser;

	try {
		// Launch browser in headless mode
		browser = await webkit.launch({ headless: true });
		const context = await browser.newContext();
		const page = await context.newPage();

		// Navigate to the route and wait for network to be idle
		await page.goto(route, { waitUntil: 'networkidle' });

		// Wait a bit more for any dynamic content to load
		await page.waitForTimeout(1000);

		// Generate PDF with options
		const pdfBuffer = await page.pdf({
			format: options?.format || 'A4',
			landscape: options?.landscape || false,
			margin: {
				top: options?.margin?.top || '1cm',
				right: options?.margin?.right || '1cm',
				bottom: options?.margin?.bottom || '1cm',
				left: options?.margin?.left || '1cm'
			},
			printBackground: options?.printBackground ?? true,
			scale: options?.scale || 1
		});

		// Generate filename if not provided
		const pdfFilename = filename || `export-${Date.now().toString().slice(0, 10)}.pdf`;

		// Save to a temporary directory or return the buffer
		// For now, we'll return the buffer as base64 for the client to handle
		const base64Pdf = pdfBuffer.toString('base64');

		return {
			success: true,
			filename: pdfFilename,
			data: base64Pdf,
			size: pdfBuffer.length
		};
	} catch (error) {
		console.error('PDF export error:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Unknown error occurred'
		};
	} finally {
		// Always close the browser
		if (browser) await browser.close();
	}
});
