import { query } from '$app/server';

import { Effect } from 'effect';

import type { KategoriSparepart, Sparepart } from '$lib/schema/general';
import { effectfulFetch } from '$lib/utils/fetch';

export const getAllSparepart = query(async () => {
	const getSparepart = effectfulFetch<Sparepart[]>('/sparepart');
	const response = await Effect.runPromise(getSparepart);

	if (response.kind !== 'success') return { data: [] as Sparepart[], total_rows: 0 };
	else return response;
});

export const getSparepartCategory = query(async () => {
	const getCategory = effectfulFetch<KategoriSparepart[]>('/kategorisparepart');
	const response = await Effect.runPromise(getCategory);

	if (response.kind !== 'success') return { data: [] as KategoriSparepart[], total_rows: 0 };
	else return response;
});
