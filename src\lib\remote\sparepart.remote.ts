import type { KategoriSparepart, Sparepart } from '$lib/schema/general';

import { query } from '$app/server';
import { effectfulFetch } from '$lib/utils/fetch';
import { Effect } from 'effect';

export const getAllSparepart = query(async () => {
	const getSparepart = effectfulFetch<Sparepart[]>('/sparepart');
	const response = await Effect.runPromise(getSparepart);

	return response;
});

export const getSparepartCategory = query(async () => {
	const getCategory = effectfulFetch<KategoriSparepart[]>('/kategorisparepart');
	const response = await Effect.runPromise(getCategory);

	return response;
});
