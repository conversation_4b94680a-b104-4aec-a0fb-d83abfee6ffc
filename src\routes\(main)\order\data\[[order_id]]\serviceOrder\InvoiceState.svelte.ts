import {
	_Customer,
	_Kendaraan,
	type Jasa,
	type Montir,
	type Paket,
	type Sparepart
} from '$lib/schema/general';
import type { OrderParts } from '$lib/schema/literal';
import { _Invoice, _Order, type Invoice, type Order } from '$lib/schema/order';
import type { Mutable } from 'effect/Types';
import { getContext, setContext } from 'svelte';

export interface CustomService {
	nama: string;
	harga: number;
}

export type Service = {
	kind: OrderParts;
	data: Paket | Jasa | Sparepart | CustomService;
	qty: number;
	harga: number;
	montir: Montir | null;
};

export interface InvoiceState {
	order: Mutable<Order>;
	invoice: Invoice;

	subtotal: number;
	total: number;
	discountByPercentage: number;
	discountByValue: number;
	ppn: number;

	service_order: [Service[], Service[], Service[], Service[]];

	editable: boolean;

	customerMode: 'terdaftar' | 'baru';
	vehicleMode: 'terdaftar' | 'baru';

	retainCustomerandVehicle(): void;
}

export default class InvoiceStateClass {
	editable = $state(true);
	#customerMode = $state<'terdaftar' | 'baru'>('baru');
	#vehicleMode = $state<'terdaftar' | 'baru'>('baru');

	order = $state<Mutable<Order>>(_Order);
	invoice = $state<Invoice>(_Invoice);

	#discountByPercentage = $state(0);
	#discountByValue = $state(this.invoice.order.diskon ?? 0);
	ppn = $state(this.invoice.order.pajak);

	service_order = $state<[Service[], Service[], Service[], Service[]]>([[], [], [], []]);

	subtotal = $derived(
		this.service_order.reduce(
			(acc: number, curr: Service[]) =>
				acc +
				curr.reduce(
					(acc2: number, curr2: { harga: number; qty: number }) => acc2 + curr2.harga * curr2.qty,
					0
				),
			0
		)
	);

	total = $derived(
		this.subtotal - this.discountByValue + (this.subtotal - this.discountByValue) * (this.ppn / 100)
	);

	constructor(_invoice: Invoice) {
		if (!_invoice.nomor_invoice) this.order = _Order;
		else this.order = _invoice.order;
	}

	retainCustomerandVehicle() {
		if (this.order.nomor_order) {
			if (this.order.customer.id_customer) this.#customerMode = 'terdaftar';
			else this.#customerMode = 'baru';

			if (this.order.kendaraan.nomor_polisi !== '') this.#vehicleMode = 'terdaftar';
			else this.#vehicleMode = 'baru';

			this.order.nomor_polisi = this.order.kendaraan.nomor_polisi;
		}
	}

	//

	get discountByPercentage() {
		return this.#discountByPercentage;
	}

	set discountByPercentage(v: number) {
		this.#discountByPercentage = v;
		this.#discountByValue = this.subtotal * (v / 100);
	}

	get discountByValue() {
		return this.#discountByValue;
	}

	set discountByValue(v: number) {
		this.#discountByValue = v;
		this.#discountByPercentage = (v / this.subtotal) * 100;
	}

	get customerMode() {
		return this.#customerMode;
	}

	set customerMode(v: 'terdaftar' | 'baru') {
		this.#customerMode = v;
		if (v === 'baru') this.vehicleMode = 'baru';

		this.order.customer = _Customer;
		this.order.alamat = _Customer.alamat;
	}

	get vehicleMode() {
		return this.#vehicleMode;
	}

	set vehicleMode(v: 'terdaftar' | 'baru') {
		this.#vehicleMode = v;

		this.order.kendaraan = _Kendaraan;
		this.order.nomor_polisi = _Kendaraan.nomor_polisi;
	}
}

const INVOICE_STATE_KEY = Symbol('@@invoice-state@@');

export function setInvoiceState(_invoice: Invoice): void {
	setContext(INVOICE_STATE_KEY, new InvoiceStateClass(_invoice));
}

export function getInvoiceState(): InvoiceState {
	return getContext<InvoiceState>(INVOICE_STATE_KEY);
}
