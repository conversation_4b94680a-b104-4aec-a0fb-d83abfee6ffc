<script lang="ts">
	import { getSparepartCategory } from '$lib/remote/sparepart.remote';
	import { getOpnameState } from './OpnameState.svelte';

	const opnameState = getOpnameState();
</script>

<svelte:boundary>
	{#snippet pending()}
		<div class="grid grid-cols-2 gap-3 sm:grid-cols-4 md:grid-cols-8">
			{#each Array(8) as _}
				<button class="btn btn-sm skeleton" aria-label=".">&nbsp;</button>
			{/each}
		</div>
	{/snippet}

	<div class="grid grid-cols-2 gap-3 px-0 sm:grid-cols-4 md:grid-cols-8">
		<input
			name="category-filter-radio"
			type="radio"
			class="btn btn-sm"
			aria-label="Semua"
			checked
			value={''}
			bind:group={opnameState.category}
		/>

		{#each (await getSparepartCategory()).data as kategori (kategori.id_kategori_sparepart)}
			<input
				name="category-filter-radio"
				type="radio"
				aria-label={kategori.nama_kategori}
				class="btn btn-sm"
				value={kategori.id_kategori_sparepart}
				bind:group={opnameState.category}
			/>
		{/each}
	</div>
</svelte:boundary>
