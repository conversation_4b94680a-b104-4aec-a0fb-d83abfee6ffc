<script lang="ts">
	import { getSparepartCategory } from '$lib/remote/category.remote';
	import Icon from '@iconify/svelte';

	let expand = $state(false);
	let category = getSparepartCategory();
</script>

<svelte:boundary>
	{#snippet pending()}
		<div class="grid grid-cols-2 gap-3 sm:grid-cols-4 md:grid-cols-8">
			<div class="skeleton h-4 rounded"></div>
		</div>
	{/snippet}

	<div class="grid grid-cols-2 gap-3 px-0 sm:grid-cols-4 md:grid-cols-8">
		{#each  as kategori (kategori.id_kategori_sparepart)}
			<input
				name="category-filter-radio"
				type="radio"
				aria-label={kategori.nama_kategori}
				class="btn btn-sm"
			/>
		{/each}
	</div>
</svelte:boundary>
