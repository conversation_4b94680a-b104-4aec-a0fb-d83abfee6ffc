<script lang="ts">
	import Currency from '$lib/inputs/Currency.svelte';
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Icon from '@iconify/svelte';
	import DeleteSparepart from './components/DeleteSparepart.svelte';
	import { getDetailState, setDetailState } from './components/detailState.svelte';
	import DetailModal from './components/DetailModal.svelte';

	const { data } = $props();

	setDetailState();
	const detailState = getDetailState();
</script>

<div class="flex h-full flex-col gap-4">
	<div class="flex shrink-0 justify-around gap-2">
		<button
			class="btn btn-primary btn-sm btn-outline"
			onclick={() => {
				detailState.modal?.showModal();
				detailState.mode = 'add';
				detailState.addNewBody();
			}}
		>
			<Icon icon="mdi:plus" /> Tambah Sparepart
		</button>

		<SearchUsingParam placeholder="Search..." />

		<!-- <button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:sort-alphabetical-ascending" />
			Sort By Ascending
		</button> -->
	</div>

	<div class="flex h-full grow gap-4 overflow-auto">
		<div class="relative z-50 flex w-5/6 flex-col gap-4">
			<div class="grid grow grid-cols-3 content-start gap-4 overflow-auto">
				{#each data.list as sparepart (sparepart.kode_sparepart)}
					<div>
						<div class="card lg:card-side bg-base-100 border border-gray-200 shadow-sm">
							{#if 'link_gambar_sparepart' in sparepart && sparepart.link_gambar_sparepart && sparepart.link_gambar_sparepart !== ''}
								<figure class="w-1/4 shrink-0">
									<img src={sparepart.link_gambar_sparepart as string} alt="Album" />
								</figure>
							{/if}
							<div class="card-body p-4">
								<div class="flex items-center justify-between">
									<h2 class="text-base font-bold">{sparepart.nama_sparepart}</h2>
									<div class="dropdown dropdown-right">
										<button tabindex="0" class="btn btn-ghost btn-circle">
											<Icon icon="mdi:dots-horizontal" font-size="1.2em" />
										</button>
										<!-- svelte-ignore a11y_no_noninteractive_tabindex -->
										<ul
											tabindex="0"
											class="dropdown-content menu bg-base-100 rounded-box z-10 w-52 border border-gray-200 p-2 shadow-lg"
										>
											<li class="border-b border-b-gray-200">
												<button
													class="btn btn-sm btn-ghost"
													onclick={() => {
														detailState.mode = 'view';
														detailState.body = sparepart;
														detailState.modal?.showModal();
													}}
												>
													<Icon icon="mdi:eye" /> Lihat Data Sparepart
												</button>
											</li>

											<li class="border-b border-b-gray-200">
												<button
													class="btn btn-sm btn-ghost"
													onclick={() => {
														detailState.mode = 'edit';
														detailState.body = sparepart;
														detailState.kategoriSparepart = sparepart.id_kategori_sparepart;
														detailState.satuanSparepart = sparepart.id_satuan;
														detailState.modal?.showModal();
													}}
												>
													<Icon icon="mdi:pencil" /> Edit Data Sparepart
												</button>
											</li>

											<li>
												<DeleteSparepart id={sparepart.kode_sparepart} />
											</li>

											<li>
												<a
													href="/sparepart/kartu_stok/{sparepart.kode_sparepart}"
													class="flex justify-center text-nowrap"
												>
													<button class="btn btn-sm btn-ghost">
														<Icon icon="mdi:file-arrow-left-right" /> Lihat Kartu Stok
													</button>
												</a>
											</li>
										</ul>
									</div>
								</div>

								<div class="tooltip" data-tip={sparepart.keterangan}>
									<p class="line-clamp-1">
										{sparepart.keterangan} &nbsp;
									</p>
								</div>

								<div class="flex items-center justify-between">
									<p class="text-neutral-400">Stok Barang : {sparepart.stok_barang}</p>
									<div class="badge badge-success rounded-full">
										{sparepart.stok_minimum}
									</div>
								</div>
							</div>
						</div>
					</div>
				{/each}
			</div>

			<div class="shrink-0">
				<PaginationUsingParam total_content={data.total_rows} />
			</div>
		</div>

		<div class="relative z-10 w-1/6 shrink-0">
			<h2 class="flex items-center gap-2 font-bold">
				<Icon icon="mdi:filter" />
				Filter
			</h2>

			<div class="divider my-2"></div>

			<h3 class="mb-2 text-sm font-semibold">Kategori</h3>

			<div class="flex flex-col gap-2">
				{#each data.kategori as kategori}
					<label class="label text-sm">
						<input
							type="checkbox"
							class="checkbox checkbox-xs"
							value={kategori.id_kategori_sparepart}
						/>
						{kategori.nama_kategori}
					</label>
				{/each}
			</div>

			<div class="divider my-2"></div>

			<h3 class="mb-2 text-sm font-semibold">Jumlah Stok</h3>

			<div class="flex flex-col gap-2">
				{#each ['> 5', '> 10', '< 5', '< 10'] as stok}
					<label class="label text-sm">
						<input type="checkbox" class="checkbox checkbox-xs" value={stok} />
						{stok}
					</label>
				{/each}
			</div>

			<div class="divider my-2"></div>

			<h3 class="mb-2 text-sm font-semibold">Batas Harga</h3>

			<label class="floating-label mb-2">
				<span>Harga Minimum</span>
				<Currency name="harga_minimum" id="harga_minimum" value={0} class="input-sm" />
			</label>

			<label class="floating-label">
				<span>Harga Maksimum</span>
				<Currency name="harga_maksimum" id="harga_maksimum" value={0} class="input-sm" />
			</label>

			<div class="divider my-2"></div>

			<button class="btn btn-outline btn-primary btn-sm w-full"> Filter </button>
		</div>
	</div>
</div>

<DetailModal list_satuan={data.satuan} />
