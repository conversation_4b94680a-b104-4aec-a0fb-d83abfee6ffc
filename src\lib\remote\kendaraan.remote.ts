import { query } from '$app/server';
import { NoPolisiSchema } from '$lib/schema/basic';
import type { Kendaraan } from '$lib/schema/general';
import { effectfulFetch } from '$lib/utils/fetch';
import { Effect, Schema } from 'effect';

export const getRiwayatService = query(
	NoPolisiSchema.pipe(Schema.standardSchemaV1),
	async (nomor_polisi) => {
		const getRiwayat = effectfulFetch<string[]>(`/kendaraan/riwayat/${nomor_polisi}`);
		const response = await Effect.runPromise(getRiwayat);

		if (response.kind === 'success') {
			return {
				data: response.data?.map((item) => ({ nomor_order: item })),
				total_rows: response.total_rows
			};
		} else return { data: [], total_rows: 0 };
	}
);

export const getKendaraanDetail = query(
	NoPolisiSchema.pipe(Schema.standardSchemaV1),
	async (nomor_polisi) => {
		const getDetail = effectfulFetch<Kendaraan>(`/kendaraan/${nomor_polisi}`);
		const response = await Effect.runPromise(getDetail);

		if (response.kind === 'success') return response.data;
		else return null;
	}
);
