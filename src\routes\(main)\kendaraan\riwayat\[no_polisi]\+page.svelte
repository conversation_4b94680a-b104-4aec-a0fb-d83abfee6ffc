<script lang="ts">
	import { getKendaraanDetail, getRiwayatService } from '$lib/remote/kendaraan.remote.js';
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';

	const { params } = $props();

	const riwayat = await getRiwayatService(params.no_polisi);
	const kendaraan = await getKendaraanDetail(params.no_polisi);
</script>

<div class="flex justify-around gap-2">
	<a href="/kendaraan" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:arrow-left" /> Kembali
		</button>
	</a>

	<SearchUsingParam placeholder="Search..." />

	<!-- <button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button> -->
</div>

<br />

<svelte:boundary>
	{#snippet pending()}
		<span class="loading loading-infinity"></span> loading vehicle data ...
	{/snippet}
	<div class="text-primary flex items-center gap-8">
		<div class="flex items-center gap-2">
			<p class="font-semibold">Nomor Polisi</p>
			<p class="w-4">:</p>
			<p class="text-sm">{kendaraan?.nomor_polisi}</p>
		</div>

		<div class="flex items-center gap-2">
			<p class="font-semibold">Nama Kendaraan</p>
			<p class="w-4">:</p>
			<p class="text-sm">{kendaraan?.nama_kendaraan}</p>
		</div>

		<div class="flex items-center gap-2">
			<p class="font-semibold">Nama Pemilik</p>
			<p class="w-4">:</p>
			<p class="text-sm">{kendaraan?.pemilik.nama}</p>
		</div>
	</div>
</svelte:boundary>

<br />

<svelte:boundary>
	{#snippet pending()}
		<span class="loading loading-infinity"></span> loading history data ...
	{/snippet}

	<Table
		table_header={[
			['numbering', 'No.'],
			['custom', 'Nomor Order']
		]}
		table_data={riwayat.data}
	>
		{#snippet custom({ header, body })}
			{#if header === 'Nomor Order'}
				<a href="/montir/riwayat/{params.no_polisi}/{body.nomor_order}" class="link link-primary">
					{body.nomor_order}
				</a>
			{/if}
		{/snippet}
	</Table>

	<br />

	<div class="flex justify-end">
		<PaginationUsingParam total_content={riwayat.total_rows} />
	</div>
</svelte:boundary>
