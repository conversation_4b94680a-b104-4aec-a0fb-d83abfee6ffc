<script lang="ts">
	import { getOrder } from '$lib/remote/order.remote';
	import OrderCard from './OrderCard.svelte';
	import Icon from '@iconify/svelte';

	import type { OrderStatus } from '$lib/schema/literal';

	interface IProps {
		kind: OrderStatus;
	}

	const { kind }: IProps = $props();
	const orders = getOrder(kind);
</script>

<svelte:boundary>
	<div
		class="rounded-lg p-4 pt-0 {kind === 'Antrian'
			? 'bg-blue-100'
			: kind === 'Dikerjakan'
				? 'bg-amber-100'
				: kind === 'Selesai'
					? 'bg-emerald-100'
					: 'bg-gray-100'}"
	>
		<div
			class="mb-4 flex items-center justify-center gap-4 border-b border-b-white py-2 text-center"
		>
			<h2 class=" text-center leading-2 font-bold tracking-wide uppercase">
				{kind}
			</h2>

			<div class="flex items-center gap-2">
				{#if kind === 'Antrian'}
					<a href="/order/data">
						<button class="btn btn-xs btn-outline">
							<Icon icon="mdi:plus" />
						</button>
					</a>
				{/if}

				<a href="/order/list/{kind}">
					<button class="btn btn-xs btn-outline">
						<Icon icon="mdi:open-in-new" />
					</button>
				</a>
			</div>
		</div>

		<div class="flex flex-col gap-4">
			{#if $effect.pending()}
				<span class="loading loading-ball loading-sm"></span>
			{/if}

			{#each (await orders) ?? [] as order, i ((order.nomor_order ?? order.nomor_polisi) + i.toString())}
				<OrderCard {order} />
			{:else}
				<p class="text-xs font-extralight tracking-wide p-4 text-center">
					tidak ada order dalam kategori ini.
				</p>
			{/each}
		</div>
	</div>
</svelte:boundary>
