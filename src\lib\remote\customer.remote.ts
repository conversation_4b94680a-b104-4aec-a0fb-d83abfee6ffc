import type { Customer } from '$lib/schema/general';
import { form } from '$app/server';

import { Effect } from 'effect';
import { effectfulFetch } from '$lib/utils/fetch';

export const postCustomer = form(async (data) => {
	const customer = JSON.parse(data.get('customer') as string) as Customer;
	const poster = effectfulFetch('/customer', {
		method: 'POST',
		body: JSON.stringify(customer)
	});

	const response = await Effect.runPromise(poster);

	if (response.kind !== 'success') return { success: false };

	return { success: true };
});
