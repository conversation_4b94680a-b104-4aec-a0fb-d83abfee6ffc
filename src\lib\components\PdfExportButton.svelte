<script lang="ts">
	import { exportAsPdf } from '$lib/remote/utility.remote';
	import { downloadPdfFromBase64, type PdfExportOptions } from '$lib/utils/pdf';
	
	interface Props {
		route?: string;
		filename?: string;
		options?: PdfExportOptions;
		variant?: 'primary' | 'secondary' | 'outline';
		size?: 'sm' | 'md' | 'lg';
		disabled?: boolean;
		class?: string;
	}
	
	let {
		route = typeof window !== 'undefined' ? window.location.href : '',
		filename,
		options = {
			format: 'A4',
			landscape: false,
			printBackground: true,
			margin: { top: '1cm', right: '1cm', bottom: '1cm', left: '1cm' }
		},
		variant = 'primary',
		size = 'md',
		disabled = false,
		class: className = '',
		...restProps
	}: Props = $props();
	
	let isExporting = $state(false);
	let exportError = $state<string | null>(null);
	
	// Compute button classes based on variant and size
	const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
	
	const variantClasses = {
		primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300',
		secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 disabled:bg-gray-300',
		outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-400'
	};
	
	const sizeClasses = {
		sm: 'px-3 py-1.5 text-sm',
		md: 'px-4 py-2 text-sm',
		lg: 'px-6 py-3 text-base'
	};
	
	const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
	
	async function handleExport() {
		if (!route) {
			exportError = 'No route specified for PDF export';
			return;
		}
		
		isExporting = true;
		exportError = null;
		
		try {
			const result = await exportAsPdf({
				route,
				filename: filename || `export-${Date.now()}.pdf`,
				options
			});
			
			if (result.success) {
				downloadPdfFromBase64(result.data, result.filename);
			} else {
				exportError = result.error;
			}
		} catch (error) {
			exportError = error instanceof Error ? error.message : 'Failed to export PDF';
		} finally {
			isExporting = false;
		}
	}
</script>

<div class="relative">
	<button
		class={buttonClasses}
		disabled={disabled || isExporting}
		onclick={handleExport}
		{...restProps}
	>
		{#if isExporting}
			<svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
				<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
				<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
			</svg>
			Exporting...
		{:else}
			<svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
			</svg>
			Export PDF
		{/if}
	</button>
	
	{#if exportError}
		<div class="absolute top-full left-0 mt-1 p-2 bg-red-100 border border-red-300 rounded text-red-700 text-sm whitespace-nowrap z-10">
			{exportError}
		</div>
	{/if}
</div>
