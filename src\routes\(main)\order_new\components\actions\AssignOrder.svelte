<script lang="ts">
	import { assignOrder, getOrder } from '$lib/remote/order.remote';

	import Icon from '@iconify/svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';
	import type { Order } from '$lib/schema/order';
	import { tick } from 'svelte';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	interface IProps {
		order: Order;
	}
	const { order }: IProps = $props();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirma<PERSON>',
				description: 'Apakah Anda yakin akan mengerjakan order ini?',
				loader: 'assign:order:' + order.nomor_order
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<form
	{...assignOrder.enhance(async ({ submit, form }) => {
		await submit().updates(
			getOrder('Dikerjakan').withOverride((orders) => [order, ...(orders ?? [])]),
			getOrder('Antrian').withOverride(
				(orders) => orders?.filter((o) => o.nomor_order !== order.nomor_order) ?? []
			)
		);
		if (assignOrder.result?.kind === 'success')
			toastState.add({
				message: 'Order berhasil dikerjakan',
				type: 'success'
			});

		confirmState.loader = '';
	})}
	class="flex items-center justify-center p-0"
>
	<input type="hidden" name="order" value={JSON.stringify(order)} />
	<button
		class="btn btn-ghost btn-sm w-full border-b"
		type="button"
		onclick={(e) => confirmation(e)}
	>
		<ConfirmLoader name="assign:order:{order.nomor_order}">
			<Icon icon="mdi:wrench-clock-outline" /> Kerjakan
		</ConfirmLoader>
	</button>
</form>
