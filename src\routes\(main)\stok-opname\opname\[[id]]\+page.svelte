<script lang="ts">
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
	import CategoryFilter from './components/CategoryFilter.svelte';
	import { getOpnameState, setOpnameState } from './components/OpnameState.svelte';
	import SparepartTable from './components/SparepartTable.svelte';
	import TableSkeleton from '$lib/table/TableSkeleton.svelte';
	import { page } from '$app/state';
	import { postOpnameDetail } from '$lib/remote/opname.remote';
	import SubmitOpnameDetail from './components/SubmitOpnameDetail.svelte';

	setOpnameState();
	const opnameState = getOpnameState();
</script>

<div class="grid grid-cols-3 gap-2">
	<fieldset class="fieldset">
		<legend class="fieldset-legend text-primary">Nomor Stok Opname</legend>
		<input
			type="text"
			disabled
			value={opnameState.opnameId ?? 'Terbuat otomatis.'}
			class="input w-full"
		/>
	</fieldset>

	<fieldset class="fieldset">
		<legend class="fieldset-legend text-primary">Tanggal Stok Opname</legend>
		<input type="date" class="input w-full" value={opnameState.date} />
	</fieldset>

	<fieldset class="fieldset">
		<legend class="fieldset-legend text-primary">Nama User</legend>
		<input type="text" disabled value={page.data.user.nama} class="input w-full" />
	</fieldset>
</div>

<div class="divider"></div>

<div class="mb-3 flex items-center justify-end gap-4">
	<label for="show_audited">
		<span class="text-xs font-light">tunjukkan hanya ter-audit</span>
		<input
			type="checkbox"
			name="show_audited"
			id="show_audited"
			class="checkbox"
			bind:checked={opnameState.showAudited}
		/>
	</label>

	<div class="btn btn-outline font-light hover:pointer-events-none">
		{opnameState.auditCount} Audit.
	</div>

	{#if !page.params.id}
		<SubmitOpnameDetail />
	{/if}
</div>

<div class="flex shrink-0 justify-around gap-2">
	<input
		type="search"
		name="keyword"
		id="keyword"
		bind:value={opnameState.keyword}
		placeholder="Cari berdasar nama sparepart..."
		class="input w-full"
	/>

	<!-- <button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button> -->
</div>

<br />

<CategoryFilter />

<br />

<svelte:boundary>
	{#snippet pending()}
		<TableSkeleton />
	{/snippet}

	<SparepartTable />
</svelte:boundary>

<br />

<!-- TEMPORARY because of Remote Function Experiment -->

<!-- <div class="flex justify-end">
	<PaginationUsingParam total_content={data.total_rows} />
</div> -->
