<script lang="ts">
	import Icon from '@iconify/svelte';

	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';
	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';

	import { postOpnameDetail } from '$lib/remote/opname.remote';
	import { goto } from '$app/navigation';
	import { getOpnameState } from './OpnameState.svelte';

	const toastState = getToastState();
	const confirmState = getConfirmState();

	const opnameState = getOpnameState();

	const confirmation = (e: Event) => {
		if (opnameState.auditCount === 0) {
			toastState.add({
				message: 'Tidak ada perubahan stok yang dilakukan',
				type: 'warning'
			});
			return;
		}

		confirmState.asking(
			{
				title: 'Konfirma<PERSON>',
				description: '<PERSON><PERSON><PERSON>h Anda yakin akan menyimpan stok opname ini?',
				loader: 'submit:opname'
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<form
	{...postOpnameDetail.enhance(async ({ submit }) => {
		await submit();

		if (postOpnameDetail.result?.success) {
			toastState.add({
				message: 'Stok Opname Berhasil Disimpan',
				type: 'success'
			});

			toastState.add({
				message: 'Pindah ke halaman awal Stok Opname...',
				type: 'info'
			});
			goto('/stok-opname');
		} else {
			toastState.add({
				message: 'Terjadi kesalahan saat proses menyimpan Stok Opname',
				type: 'error'
			});
		}

		confirmState.loader = '';
	})}
>
	<input
		type="hidden"
		name="opname_detail"
		value={JSON.stringify(
			opnameState.sparepart.filter((item) => item.stok_fisik !== item.stok_sistem)
		)}
	/>

	<button class="btn btn-primary" type="button" onclick={(e) => confirmation(e)}>
		<ConfirmLoader name="submit:opname">
			<Icon icon="mdi:content-save" /> Simpan Stok Opname
		</ConfirmLoader>
	</button>
</form>
