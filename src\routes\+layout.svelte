<script lang="ts">
	import '@fontsource-variable/lexend';
	import '../app.css';

	import { navigating } from '$app/state';
	import { expoOut } from 'svelte/easing';
	import { slide } from 'svelte/transition';

	import Confirm from '$lib/utils/confirm/Confirm.svelte';
	import Toast from '$lib/utils/toast/Toast.svelte';
	import { setConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { setToastState } from '$lib/utils/toast/ToastState.svelte';

	let { children } = $props();

	setToastState();
	setConfirmState();
</script>

{#if navigating.type === 'link' || navigating.type === 'leave' || navigating.type === 'goto' || navigating.type === 'popstate'}
	<div
		class="bg-secondary fixed top-0 right-0 left-0 z-50 h-1 w-full"
		in:slide={{ delay: 100, duration: 12000, axis: 'x', easing: expoOut }}
	></div>
{/if}

<svelte:boundary>
	{#snippet failed(error, reset)}
		<main class="grid h-screen place-items-center">
			<div class="text-center">
				<p class="text-error mb-2 italic">
					{error}
				</p>

				<button onclick={reset} class="btn btn-accent btn-soft">Try Again!</button>
			</div>
		</main>
	{/snippet}

	{#snippet pending()}
		<main class="grid h-screen place-items-center">
			<div>
				<span class="loading loading-infinity"></span>
				Loading Initial Data
			</div>
		</main>
	{/snippet}

	<div class="overflow-auto">
		{@render children()}
	</div>

	<Toast />
	<Confirm />
</svelte:boundary>
