<script lang="ts">
	import type { Order } from '$lib/schema/order';

	import { calculateTimeDifference } from '$lib/scripts/dates';

	import TimeElapsed from '../../order/list/[[status]]/components/TimeElapsed.svelte';
	import AssignOrder from './actions/AssignOrder.svelte';
	import { slide } from 'svelte/transition';
	import DeleteOrder from './actions/DeleteOrder.svelte';
	import VoidInvoice from './actions/VoidInvoice.svelte';

	interface IProps {
		order: Order;
	}

	const { order }: IProps = $props();

	const startTime = order.status !== 'Dikerjakan' ? order.created_at : order.updated_at;
</script>

<div class="card glass card-bg bg-white/90 shadow" transition:slide>
	<div class="card-body gap-0 px-0 py-2 pb-0">
		<div class="card-title justify-center border-b pb-1">
			<div>{order.kendaraan.nomor_polisi}</div>
		</div>
		<div class="py-0.5 text-center text-xs leading-none font-light shadow">
			{order.nomor_invoice ?? order.nomor_order}
		</div>
		<div class="grid grid-cols-2">
			<div class="border-e p-2 text-center tracking-widest">
				{startTime?.split('T')[1].replace('Z', '')}
			</div>
			<div class="grid place-items-center p-2">
				{#if order.status !== 'Selesai' && order.status !== 'Void'}
					<TimeElapsed start={order.created_at} />
				{:else}
					<p class="text-xs">{calculateTimeDifference(order.created_at)}</p>
				{/if}
			</div>
		</div>

		<div class="card-actions absolute right-2 flex items-center gap-1">
			<button
				class="btn btn-ghost btn-circle btn-xs"
				style="anchor-name: --anchor-menu-{order.nomor_order};"
				popovertarget="popover-menu-{order.nomor_order}">&vellip;</button
			>

			<a href="/order/data/{order.nomor_order}">
				<button class="btn btn-ghost btn-circle btn-xs"> &UpperRightArrow; </button>
			</a>
		</div>

		<ul
			class="dropdown menu rounded-box bg-base-100 w-52 overflow-hidden p-0 shadow-lg"
			popover
			id="popover-menu-{order.nomor_order}"
			style="position-anchor: --anchor-menu-{order.nomor_order}; position-area: center start"
		>
			{#if order.status === 'Antrian'}
				<li><AssignOrder {order} /></li>
				<li><DeleteOrder {order} /></li>
			{/if}
			{#if order.nomor_invoice}
				<li><VoidInvoice {order} /></li>
			{/if}
		</ul>
	</div>
</div>

<style>
	.card-bg {
		background-image:
			repeating-linear-gradient(
				0deg,
				transparent,
				transparent 20px,
				rgba(75, 85, 99, 0.08) 20px,
				rgba(75, 85, 99, 0.08) 21px
			),
			repeating-linear-gradient(
				90deg,
				transparent,
				transparent 30px,
				rgba(107, 114, 128, 0.06) 30px,
				rgba(107, 114, 128, 0.06) 31px
			),
			repeating-linear-gradient(
				60deg,
				transparent,
				transparent 40px,
				rgba(55, 65, 81, 0.05) 40px,
				rgba(55, 65, 81, 0.05) 41px
			),
			repeating-linear-gradient(
				150deg,
				transparent,
				transparent 35px,
				rgba(31, 41, 55, 0.04) 35px,
				rgba(31, 41, 55, 0.04) 36px
			);
	}
</style>
