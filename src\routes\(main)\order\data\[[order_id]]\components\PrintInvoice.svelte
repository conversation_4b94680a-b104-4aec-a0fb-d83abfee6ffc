<script lang="ts">
	import Icon from '@iconify/svelte';
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte';
	import { exportAsPdf } from '$lib/remote/utility.remote';
	import { downloadPdfFromBase64 } from '$lib/utils/pdf';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';

	const invoiceState = getInvoiceState();

	const toastState = getToastState();
</script>

{#if invoiceState?.invoice?.nomor_invoice}
	<button
		class="btn btn-primary btn-sm btn-outline w-fit"
		onclick={async () => {
			const result = await exportAsPdf({
				route: `/order/data/${invoiceState.order.nomor_order}`,
				filename: `invoice-${invoiceState.invoice.nomor_invoice}.pdf`
			});

			if (result.success) {
				downloadPdfFromBase64(result.data, result.filename ?? '');
			} else {
				toastState.add({
					message: 'Gagal mencetak invoice',
					type: 'error'
				});
			}
		}}
	>
		<Icon icon="mdi:printer-outline" font-size="1.2rem" /> Print Invoice
	</button>
{/if}
