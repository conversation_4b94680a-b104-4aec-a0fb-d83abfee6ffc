<script lang="ts">
	import Icon from '@iconify/svelte';
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte';
	import { exportAsPdf } from '$lib/remote/utility.remote';

	const invoiceState = getInvoiceState();
</script>

{#if invoiceState?.invoice?.nomor_invoice}
	<button
		class="btn btn-primary btn-sm btn-outline w-fit"
		onclick={async () => {
			await exportAsPdf(`/order/data/${invoiceState.order.nomor_order}`);
		}}
	>
		<Icon icon="mdi:printer-outline" font-size="1.2rem" /> Print Invoice
	</button>
{/if}
