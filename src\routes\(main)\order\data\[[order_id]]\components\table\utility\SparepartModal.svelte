<script lang="ts">
	import type { Sparepart } from '$lib/schema/general';

	import { rupiah } from '$lib/inputs/CurrencyState.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';

	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import { getInvoiceState } from '../../../serviceOrder/InvoiceState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';

	import Icon from '@iconify/svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
	const invoiceState = getInvoiceState();
	const toastState = getToastState();

	const groupIndex = $derived(
		invoiceState.service_order.findIndex((service) =>
			service.find((order) => order.kind === 'Sparepart')
		)
	);

	const group = $derived(groupIndex === -1 ? [] : invoiceState.service_order[groupIndex]);

	interface IProps {
		onTable?: boolean;
	}
	const { onTable = false }: IProps = $props();
</script>

{#if onTable}
	<button
		class="btn btn-xs btn-outline btn-soft"
		onclick={() => utilityModalState.modal?.showModal()}
	>
		-- Tambah Sparepart -- <Icon icon="majesticons:chevron-down" />
	</button>
{:else}
	<button class="btn btn-outline w-full" onclick={() => utilityModalState.modal?.showModal()}>
		Sparepart
	</button>
{/if}

<UtilityModal url="/sparepart">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Sparepart</h2>
	{/snippet}

	{#snippet item({ item }: { item: Sparepart })}
		{@const alreadySelected = group.find(
			(order) =>
				order.kind === 'Sparepart' &&
				(order.data as Sparepart).kode_sparepart === item.kode_sparepart
		)}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start"
			class:btn-disabled={alreadySelected}
			onclick={() => {
				if (alreadySelected) {
					toastState.add({
						message: 'Sparepart sudah dipilih',
						type: 'error'
					});
					return;
				}

				const availableGroupIndex =
					groupIndex === -1
						? invoiceState.service_order.findIndex((service) => service.length === 0)
						: groupIndex;

				invoiceState.service_order[availableGroupIndex].push({
					kind: 'Sparepart',
					data: item,
					qty: 1,
					harga: item.harga_jual,
					montir: null
				});
			}}
		>
			{item.nama_sparepart} <span class="font-light"> @ {rupiah(item.harga_jual)}</span>
		</button>
	{/snippet}
</UtilityModal>
