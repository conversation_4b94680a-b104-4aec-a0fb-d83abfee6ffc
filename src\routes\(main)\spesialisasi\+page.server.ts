import type { Actions, PageServerLoad } from './$types';
import {
	SpesialisasiSchema,
	type Spesialisasi,
	type SpesialisasiEncoded
} from '$lib/schema/general';

import { Effect } from 'effect';
import { effectfulFetch } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ url }) => {
	// const limit = url.searchParams.get('limit') ?? '10';
	// const offset = url.searchParams.get('offset') ?? '1';
	// const keyword = url.searchParams.get('keyword') ?? '';

	const getter = effectfulFetch<Spesialisasi[]>(`/spesialisasi`);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success') return { list: [], total_rows: 0 };

	return { list: response.data, total_rows: response.total_rows };
};

export const actions: Actions = {
	'action:spesialisasi': async ({ request }) => {
		const data = await request.formData();
		const spesialisasi = JSON.parse(data.get('spesialisasi') as string) as Spesialisasi;
		const mode = data.get('mode');

		const decoded = decodeForm<Spesialisasi, SpesialisasiEncoded>(SpesialisasiSchema, spesialisasi);
		if ('error' in decoded) return fail(400, { errors: decoded.errors });

		const action = effectfulFetch('/spesialisasi', {
			method: mode === 'add' ? 'POST' : 'PUT',
			body: JSON.stringify({
				id_spesialisasi: spesialisasi.id_spesialisasi,
				nama_spesialisasi: spesialisasi.nama
			})
		});

		const response = Effect.runPromise(action);

		return response;
	},
	'delete:spesialisasi': async ({ request }) => {
		const data = await request.formData();
		const id = data.get('id');

		const action = effectfulFetch(`/spesialisasi/${id}`, {
			method: 'DELETE'
		});

		const response = Effect.runPromise(action);
		return response;
	}
};
