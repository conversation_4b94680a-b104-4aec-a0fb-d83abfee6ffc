import { query } from '$app/server';

import type { StokOpname } from '$lib/schema/general';

import { Effect, Schema } from 'effect';
import { effectfulFetch } from '$lib/utils/fetch';

export const getOpnameDetail = query(
	Schema.String.pipe(Schema.standardSchemaV1),
	async (id_opname) => {
		const getOpname = effectfulFetch<StokOpname>(`/stock/opname/${id_opname}`);
		const response = await Effect.runPromise(getOpname);

		if (response.kind === 'success') return response.data;
		else return null;
	}
);
