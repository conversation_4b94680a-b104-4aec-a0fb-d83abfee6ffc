import { form, getRequestEvent, query } from '$app/server';

import { type DetailOpname } from '$lib/schema/general';

import { Effect, Schema } from 'effect';
import { effectfulFetch } from '$lib/utils/fetch';
import { env } from '$env/dynamic/public';

export const getOpnameDetail = query(
	Schema.String.pipe(Schema.standardSchemaV1),
	async (id_opname) => {
		const getOpname = effectfulFetch<DetailOpname[]>(`/stock/opname/detail/${id_opname}`);
		const response = await Effect.runPromise(getOpname);

		if (response.kind === 'success') return response.data;
		else return [];
	}
);

export const postOpnameDetail = form(async (data) => {
	const event = getRequestEvent();

	const poster = effectfulFetch<{ id_opname: string }>('/stock/opname', {
		method: 'POST',
		body: JSON.stringify({
			id_bengkel: env.PUBLIC_ID_BENGKEL,
			id_karyawan: event.locals.auth.id
		})
	});

	const response = await Effect.runPromise(poster);

	if (response.kind !== 'success') return { success: false };

	//

	const opname_detail = JSON.parse(data.get('opname_detail') as string) as DetailOpname[];

	const detail_poster = effectfulFetch('/stock/opname/detail', {
		method: 'POST',
		body: JSON.stringify(
			opname_detail.map((item) => ({
				...item,
				id_opname: response.data?.id_opname, // TEMPORARY
				kode_sparepart: item.kartustok.sparepart.kode_sparepart,
				selisih: item.stok_fisik - item.stok_sistem
			}))
		)
	});

	const response_detail = await Effect.runPromise(detail_poster);

	if (response_detail.kind !== 'success') return { success: false };
	return { success: true };
});
