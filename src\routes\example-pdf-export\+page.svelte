<script lang="ts">
	import { exportAsPdf } from '$lib/remote/utility.remote';
	import { downloadPdfFromBase64, openPdfInNewTab, type PdfExportOptions } from '$lib/utils/pdf';
	
	let isExporting = false;
	let exportStatus = '';
	
	// Example PDF export options
	const defaultOptions: PdfExportOptions = {
		format: 'A4',
		landscape: false,
		margin: {
			top: '1cm',
			right: '1cm',
			bottom: '1cm',
			left: '1cm'
		},
		printBackground: true,
		scale: 1
	};
	
	async function handleExportPdf(route: string, filename?: string, options?: PdfExportOptions) {
		isExporting = true;
		exportStatus = 'Generating PDF...';
		
		try {
			const result = await exportAsPdf({
				route,
				filename,
				options: options || defaultOptions
			});
			
			if (result.success) {
				exportStatus = `PDF generated successfully! Size: ${(result.size / 1024).toFixed(2)} KB`;
				
				// Download the PDF
				downloadPdfFromBase64(result.data, result.filename);
				
				// Or open in new tab instead:
				// openPdfInNewTab(result.data);
				
			} else {
				exportStatus = `Error: ${result.error}`;
			}
		} catch (error) {
			exportStatus = `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
		} finally {
			isExporting = false;
		}
	}
	
	// Example routes to export
	const exampleRoutes = [
		{ name: 'Current Page', route: window.location.href },
		{ name: 'Home Page', route: '/' },
		{ name: 'Paket Page', route: '/paket' },
		{ name: 'Spesialisasi Page', route: '/spesialisasi' }
	];
</script>

<div class="container mx-auto p-6">
	<h1 class="text-3xl font-bold mb-6">PDF Export Example</h1>
	
	<div class="space-y-4">
		<div class="bg-white p-4 rounded-lg shadow">
			<h2 class="text-xl font-semibold mb-4">Export Pages as PDF</h2>
			
			<div class="grid gap-4 md:grid-cols-2">
				{#each exampleRoutes as { name, route }}
					<button
						class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
						disabled={isExporting}
						onclick={() => handleExportPdf(route, `${name.toLowerCase().replace(' ', '-')}.pdf`)}
					>
						Export {name}
					</button>
				{/each}
			</div>
		</div>
		
		<div class="bg-white p-4 rounded-lg shadow">
			<h2 class="text-xl font-semibold mb-4">Custom Export Options</h2>
			
			<button
				class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
				disabled={isExporting}
				onclick={() => handleExportPdf('/', 'landscape-home.pdf', {
					format: 'A4',
					landscape: true,
					margin: { top: '2cm', right: '2cm', bottom: '2cm', left: '2cm' },
					printBackground: true,
					scale: 0.8
				})}
			>
				Export Home (Landscape, Custom Margins)
			</button>
		</div>
		
		{#if exportStatus}
			<div class="bg-gray-100 p-4 rounded-lg">
				<p class="text-sm {isExporting ? 'text-blue-600' : exportStatus.includes('Error') ? 'text-red-600' : 'text-green-600'}">
					{exportStatus}
				</p>
				
				{#if isExporting}
					<div class="mt-2">
						<div class="w-full bg-gray-200 rounded-full h-2">
							<div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 60%"></div>
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
	
	<div class="mt-8 bg-yellow-50 p-4 rounded-lg">
		<h3 class="text-lg font-semibold mb-2">Usage Instructions:</h3>
		<ol class="list-decimal list-inside space-y-1 text-sm">
			<li>Make sure Playwright is installed: <code class="bg-gray-200 px-1 rounded">npm install playwright</code></li>
			<li>Install browser binaries: <code class="bg-gray-200 px-1 rounded">npx playwright install webkit</code></li>
			<li>Click any export button above to generate a PDF</li>
			<li>The PDF will be automatically downloaded to your default downloads folder</li>
		</ol>
	</div>
</div>

<style>
	code {
		font-family: 'Courier New', monospace;
	}
</style>
