<script lang="ts">
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
	import { retrieveAll } from './opname.remote';

	const allData = retrieveAll();
</script>

<div class="flex justify-around gap-2">
	<a href="/stok-opname/opname" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:plus" /> Buat SO
		</button>
	</a>

	<SearchUsingParam placeholder="Search..." />

	<!-- <button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button> -->
</div>

<br />

<svelte:boundary>
	{#if allData.error}
		<p class="text-center">oops!</p>
	{:else if allData.loading}
		<p class="grid place-items-center"><span class="loading loading-infinity"></span> loading...</p>
	{:else if allData.current}
		<Table
			table_header={[
				['numbering', 'No.'],
				['custom', 'ID Stok Opname'],
				['created_at', 'Tanggal Stok Opname', 'date'],
				['custom', 'Karyawan'],
				['custom', 'Actions']
			]}
			table_data={allData.current}
		>
			{#snippet custom({ header, body })}
				{#if header === 'Actions'}
					<div class="flex items-center gap-2">
						<button class="btn btn-sm btn-outline btn-primary uppercase">
							<Icon icon="mdi:open-in-new" />
						</button>
						<button class="btn btn-sm btn-outline btn-primary uppercase">
							<Icon icon="mdi:pencil" />
						</button>
						<button class="btn btn-sm btn-outline btn-primary uppercase">
							<Icon icon="mdi:trash-can" />
						</button>
					</div>
				{:else if header === 'ID Stok Opname'}
					<a href="/stok-opname/opname/{body.id_opname}" class="link">
						{body.id_opname}
					</a>
				{:else if header === 'Karyawan'}
					{body.karyawan.nama}
				{/if}
			{/snippet}
		</Table>
	{/if}
</svelte:boundary>

<br />

<!-- TEMPORARY because of Remote Function Experiment -->
<!-- 
<div class="flex justify-end">
	<PaginationUsingParam total_content={data.} />
</div> -->
