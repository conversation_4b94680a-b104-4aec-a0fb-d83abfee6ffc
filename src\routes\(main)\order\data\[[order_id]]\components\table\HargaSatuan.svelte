<script lang="ts">
	import { getInvoiceState, type Service } from '../../serviceOrder/InvoiceState.svelte';
	import Currency from '$lib/inputs/Currency.svelte';

	interface IProps {
		body: Service[];
		index: number;
	}
	const { body, index: groupIndex }: IProps = $props();
	const kind = $derived(body[0]?.kind);

	const invoiceState = getInvoiceState();
</script>

{#if !invoiceState.editable}
	&nbsp;
	<div class="mb-1"></div>
{/if}

<div class="flex flex-col gap-1">
	<ol class="flex flex-col">
		{#if invoiceState.service_order[groupIndex]}
			{#each body as b, index}
				<li
					class="mb-1 w-2/3 border-gray-200"
					class:border-b={kind !== 'Custom' || !invoiceState.editable}
				>
					{#if invoiceState.service_order[groupIndex][index].harga}
						<Currency
							name="harga"
							id="harga"
							bind:value={invoiceState.service_order[groupIndex][index].harga}
							class="input-xs w-32 {kind !== 'Custom' || !invoiceState.editable
								? 'input-ghost'
								: ''}"
							readonly={kind !== 'Custom'}
						/>
					{/if}
				</li>
			{/each}
		{/if}
	</ol>

	{#if (kind === 'Jasa' || kind === 'Sparepart') && invoiceState.editable}
		<p>&nbsp;</p>
	{/if}
</div>
