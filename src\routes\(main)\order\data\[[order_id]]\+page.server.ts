import { env } from '$env/dynamic/public';
import type { Actions, PageServerLoad } from './$types';

import {
	CustomerSchema,
	KendaraanSchema,
	type Customer,
	type Jasa,
	type Kendaraan,
	type Montir,
	type Paket,
	type Sparepart
} from '$lib/schema/general';

import type { Mutable } from 'effect/Types';

import { _Invoice, OrderSchema, type Invoice, type Order, _Order } from '$lib/schema/order';

import type { Service, CustomService } from './serviceOrder/InvoiceState.svelte';
import { fail, isActionFailure, type ActionFailure } from '@sveltejs/kit';
import { Effect, Schema } from 'effect';
import { launch_fetch, effectfulFetch } from '$lib/utils/fetch';
import { decodeForm, type DecodeFormValueErrors } from '$lib/utils/validation';
import { statusTransaksi } from '$lib/schema/literal';
import { getOrderParts } from './order.remote';

export const load: PageServerLoad = async ({ params }) => {
	let invoice: Mutable<Invoice> = _Invoice;
	let service_order: Service[][] = [];

	if (params.order_id) {
		const getOrder = effectfulFetch<Order>(`/order/${params.order_id}`);
		const _order = await Effect.runPromise(getOrder);
		const order = _order.kind === 'success' ? (_order.data ?? _Order) : _Order;

		invoice.order = order;

		service_order = await getServiceOrder(params.order_id);

		if (order.status === 'Selesai') {
			const getInvoice = effectfulFetch<Invoice>(`/invoice/${params.order_id}`);
			const _invoice = await Effect.runPromise(getInvoice);

			console.log(`/invoice/${params.order_id}`);
			console.log(_invoice);

			invoice.nomor_invoice =
				_invoice.kind === 'success' ? (_invoice.data?.nomor_invoice ?? '') : '';
		}
	}

	return { invoice, service_order };
};

type ServiceOrder = [Service[], Service[], Service[], Service[]];

export const actions: Actions = {
	'submit:order': async ({ request, locals }) => {
		const data = await request.formData();

		///////////////////////////////////////

		const service_order = JSON.parse(data.get('service_order') as string) as ServiceOrder;

		const CHECKER_1 = checkServiceOrder(service_order);
		if (isActionFailure(CHECKER_1)) return CHECKER_1;

		///////////////////////////////////////

		const order = JSON.parse(data.get('order') as string) as Order;

		const CHECKER_2 = checkOrder(order);
		if (isActionFailure(CHECKER_2)) return CHECKER_2;

		///////////////////////////////////////

		const subtotal = Number(data.get('subtotal') as string);
		// const discount_by_percentage = Number(data.get('discount_by_percentage') as string);
		const discount_by_value = Number(data.get('discount_by_value') as string);
		// const ppn = Number(data.get('ppn') as string);
		const total = Number(data.get('total') as string);

		const CHECKER_3 = checkTotal(subtotal, total);
		if (isActionFailure(CHECKER_3)) return CHECKER_3;

		///////////////////////////////////////

		// CHECK IF CUSTOMER IS PRESENT //
		// IF NOT CREATE NEW CUSTOMER //

		if (order.customer.id_customer === '') {
			order.customer.alamat.jalan = order.alamat.jalan;
			const responseCustomer = await postCustomer(order.customer);
			if (isActionFailure(responseCustomer)) return responseCustomer;

			if ('id_customer' in responseCustomer.data)
				order.customer.id_customer = responseCustomer.data.id_customer;
		}

		//////////////////////////////////////

		// CHECK IF VEHICLE IS PRESENT //
		// IF NOT CREATE NEW VEHICLE //

		if (order.kendaraan.id_kendaraan === '') {
			order.kendaraan.nomor_polisi = order.nomor_polisi;
			const responseVehicle = await postVehicle(order.kendaraan);
			if (isActionFailure(responseVehicle)) return responseVehicle;
		}
		//////////////////////////////////////

		// CREATE THE ORDER //

		const responseOrder = await postOrder(
			order,
			env.PUBLIC_ID_BENGKEL,
			locals.auth.id,
			discount_by_value
		);

		if (isActionFailure(responseOrder)) return responseOrder;

		///////////////////////////////////////

		// CREATE THE ORDER SERVICES //

		if ('nomor_order' in responseOrder.data) {
			const responseOrderServices = await postServiceOrder(
				service_order,
				responseOrder.data.nomor_order
			);

			if (isActionFailure(responseOrderServices)) return responseOrderServices;
		}

		return { message: 'Order created successfully.' };

		///////////////////////////////////////

		// CREATE THE INVOICE //

		const responseOrderData = responseOrder.data as { created_at: string; nomor_order: string };
		const postInvoice = launch_fetch('/invoice', {
			method: 'POST',
			body: JSON.stringify({
				nomor_order: responseOrderData.nomor_order,
				id_karyawan: locals.auth.id,
				subtotal,
				total,
				status_transaksi: statusTransaksi[0], // Belum Dibayar
				keterangan: ''
			})
		});

		const responseInvoice = await Effect.runPromise(postInvoice);

		if (isActionFailure(responseInvoice)) return responseInvoice;

		return { order, service_order };
	},

	'create:invoice': async ({ request, locals }) => {
		const formData = await request.formData();

		const order = JSON.parse(formData.get('order') as string) as Order;
		const subtotal = Number(formData.get('subtotal') as string);
		const total = Number(formData.get('total') as string);
		const metode_pembayaran = formData.get('metode_pembayaran') as string;

		const responseOrder = await postOrder(
			{ ...order, status_order: 'Selesai' },
			order.bengkel.id_bengkel,
			order.karyawan.id_karyawan,
			order.diskon,
			'PUT'
		);
		if (isActionFailure(responseOrder)) return responseOrder;

		const postInvoice = launch_fetch('/invoice', {
			method: 'POST',
			body: JSON.stringify({
				nomor_order: order.nomor_order,
				id_karyawan: locals.auth.id,
				subtotal,
				total,
				metode_pembayaran, // TEMPORARY : API should provide it here (invoice) not in order
				status_transaksi: statusTransaksi[0], // Belum Dibayar
				keterangan: ''
			})
		});

		const responseInvoice = await Effect.runPromise(postInvoice);
		return responseInvoice;
	}
};

const checkServiceOrder = (
	service_order: ServiceOrder
): ActionFailure<{ message: string }> | true => {
	if (service_order[0].length === 0)
		return fail(400, {
			message: 'Service order tidak boleh kosong.'
		});

	if (service_order.some((service) => service.some((item) => !item.montir)))
		return fail(400, {
			message: 'Montir harus diisi semua.'
		});

	return true;
};

const checkOrder = (order: Order): ActionFailure<{ errors: DecodeFormValueErrors }> | true => {
	const FormOrderSchema = Schema.Struct({
		...OrderSchema.omit('karyawan', 'customer', 'kendaraan').fields,
		customer: Schema.Struct({ ...CustomerSchema.omit('alamat', 'username', 'password').fields }),
		kendaraan: Schema.Struct({
			...KendaraanSchema.omit('pemilik', 'nomor_polisi', 'id_kendaraan').fields
		})
	});

	const decoded = decodeForm<typeof FormOrderSchema.Type, typeof FormOrderSchema.Encoded>(
		FormOrderSchema,
		order
	);

	if ('error' in decoded) return fail(400, { errors: decoded.errors });
	else return true;
};

const checkTotal = (subtotal: number, total: number): ActionFailure<{ message: string }> | true => {
	if (subtotal === 0 || total === 0)
		return fail(400, {
			message: 'Total tidak boleh kosong (Rp 0).'
		});

	return true;
};

const postOrder = async (
	order: Order,
	id_bengkel: string,
	id_karyawan: string,
	diskon: number,
	method: 'POST' | 'PUT' = 'POST'
): Promise<
	ActionFailure<{ message: string }> | { data: { created_at: string; nomor_order: string } }
> => {
	const postOrder = launch_fetch('/order', {
		method,
		body: JSON.stringify({
			...order,
			id_bengkel,
			id_karyawan,
			id_customer: order.customer.id_customer,
			diskon
		})
	});

	return await Effect.runPromise(postOrder);
};

const postCustomer = async (
	customer: Mutable<Customer>
): Promise<ActionFailure<{ message: string }> | { data: { id_customer: string } }> => {
	const default_username =
		customer.nama
			.toLowerCase()
			.replace(/[^a-z0-9]/g, '_')
			.replace(/_+/g, '_')
			.replace(/_$/, '') +
		'_' +
		Math.floor(Math.random() * 100000);

	customer.username = default_username;
	const postCustomer = launch_fetch('/customer', {
		method: 'POST',
		body: JSON.stringify(customer)
	});

	// TEMPORARY MESSAGE : /customer doesn't provide return id
	return await Effect.runPromise(postCustomer);
};

const postVehicle = async (
	vehicle: Kendaraan
): Promise<ActionFailure<{ message: string }> | { data: { nomor_polisi: string } }> => {
	const postVehicle = launch_fetch('/kendaraan', {
		method: 'POST',
		body: JSON.stringify(vehicle)
	});

	return await Effect.runPromise(postVehicle);
};

const postServiceOrder = async (
	service_order: ServiceOrder,
	nomor_order: string
): Promise<ActionFailure | true> => {
	for (const services of service_order) {
		if (services.length === 0) continue;
		const kind = services[0].kind === 'Custom' ? 'Jasa' : services[0].kind;

		const payload = services.map((service) => {
			const baseData = {
				nomor_order,
				id_montir: (service.montir as Montir).id_montir,
				kuantitas: service.qty,
				harga: service.harga,
				keterangan: '',
				created_at: '',
				updated_at: null,
				deleted_at: null
			};

			let refactored;

			if (service.kind === 'Paket') {
				refactored = {
					...baseData,
					id_paket: (service.data as Paket).id_paket,
					nama_paket: (service.data as Paket).nama_paket,
					jasa_paket: (service.data as Paket).jasa_paket,
					sparepart_paket: (service.data as Paket).sparepart_paket
				};
			} else if (service.kind === 'Jasa' || service.kind === 'Custom') {
				refactored = {
					...baseData,
					id_jasa: service.kind === 'Jasa' ? (service.data as Jasa).id_jasa : '',
					nama_jasa:
						service.kind === 'Jasa'
							? (service.data as Jasa).nama_jasa
							: (service.data as CustomService).nama
				};
			} else {
				// sparepart
				refactored = {
					...baseData,
					kode_sparepart: (service.data as Sparepart).kode_sparepart,
					nama_sparepart: (service.data as Sparepart).nama_sparepart
				};
			}

			return refactored;
		});

		const postOrderServices = launch_fetch(`/order/${kind}`, {
			method: 'POST',
			body: JSON.stringify(payload)
		});

		const responseOrderServices = await Effect.runPromise(postOrderServices);
		if (isActionFailure(responseOrderServices)) return responseOrderServices;
	}

	return true;
};

const getServiceOrder = async (order_id: string): Promise<Service[][]> => {
	let service_order: Service[][] = [];

	service_order.push(await getOrderParts({ order_id, kind: 'Jasa' }));
	service_order.push(await getOrderParts({ order_id, kind: 'Custom' }));
	service_order.push(await getOrderParts({ order_id, kind: 'Paket' }));
	service_order.push(await getOrderParts({ order_id, kind: 'Sparepart' }));

	return service_order;
};
