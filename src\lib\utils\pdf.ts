/**
 * Utility functions for handling PDF operations on the client side
 */

/**
 * Downloads a PDF from base64 data
 * @param base64Data - The base64 encoded PDF data
 * @param filename - The filename for the downloaded PDF
 */
export function downloadPdfFromBase64(base64Data: string, filename: string) {
	// Convert base64 to blob
	const byteCharacters = atob(base64Data);
	const byteNumbers = new Array(byteCharacters.length);
	
	for (let i = 0; i < byteCharacters.length; i++) {
		byteNumbers[i] = byteCharacters.charCodeAt(i);
	}
	
	const byteArray = new Uint8Array(byteNumbers);
	const blob = new Blob([byteArray], { type: 'application/pdf' });
	
	// Create download link
	const url = URL.createObjectURL(blob);
	const link = document.createElement('a');
	link.href = url;
	link.download = filename;
	
	// Trigger download
	document.body.appendChild(link);
	link.click();
	
	// Cleanup
	document.body.removeChild(link);
	URL.revokeObjectURL(url);
}

/**
 * Opens a PDF in a new tab from base64 data
 * @param base64Data - The base64 encoded PDF data
 */
export function openPdfInNewTab(base64Data: string) {
	// Convert base64 to blob
	const byteCharacters = atob(base64Data);
	const byteNumbers = new Array(byteCharacters.length);
	
	for (let i = 0; i < byteCharacters.length; i++) {
		byteNumbers[i] = byteCharacters.charCodeAt(i);
	}
	
	const byteArray = new Uint8Array(byteNumbers);
	const blob = new Blob([byteArray], { type: 'application/pdf' });
	
	// Open in new tab
	const url = URL.createObjectURL(blob);
	window.open(url, '_blank');
	
	// Note: We don't revoke the URL immediately as the new tab needs it
	// The browser will handle cleanup when the tab is closed
}

/**
 * Type definitions for PDF export options
 */
export type PdfExportOptions = {
	format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal';
	landscape?: boolean;
	margin?: {
		top?: string;
		right?: string;
		bottom?: string;
		left?: string;
	};
	printBackground?: boolean;
	scale?: number;
};

export type PdfExportResult = {
	success: true;
	filename: string;
	data: string; // base64 encoded PDF
	size: number;
} | {
	success: false;
	error: string;
};
