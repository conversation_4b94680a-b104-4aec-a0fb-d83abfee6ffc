import type { StokOpname } from '$lib/schema/general';
import { query } from '$app/server';

import { effectfulFetch } from '$lib/utils/fetch';

import { Effect } from 'effect';

export const retrieveAll = query(async () => {
	const getStokOpname = effectfulFetch<StokOpname[]>('/stock/opname');
	const response = await Effect.runPromise(getStokOpname);

	if (response.kind === 'success') {
		return response.data;
	} else {
		return [];
	}
});
