import type { OrderJasa, OrderPaket, OrderSparepart } from '$lib/schema/order';
import { Effect, Schema } from 'effect';
import { query } from '$app/server';

import { ORDER_PARTS } from '$lib/schema/literal';
import { effectfulFetch } from '$lib/utils/fetch';
import type { Service } from './serviceOrder/InvoiceState.svelte';

const OrderPartsSchema = Schema.Struct({
	order_id: Schema.String,
	kind: Schema.Literal(...ORDER_PARTS)
}).pipe(Schema.standardSchemaV1);

export const getOrderParts = query(OrderPartsSchema, async ({ order_id, kind }) => {
	const getter = effectfulFetch<OrderJasa[] | OrderPaket[] | OrderSparepart[]>(
		`/order/${kind === 'Custom' ? 'jasa' : kind.toLowerCase()}/${order_id}`
	);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success') return [];

	let initial_response: OrderJasa[] | OrderPaket[] | OrderSparepart[] = [];
	let mapped_response: Service[] = [];

	if (kind === 'Jasa')
		initial_response = (response.data?.filter((item) => (item as OrderJasa).jasa !== null) ??
			[]) as OrderJasa[];
	else if (kind === 'Paket') initial_response = (response.data ?? []) as OrderPaket[];
	else if (kind === 'Sparepart') initial_response = (response.data ?? []) as OrderSparepart[];
	else if (kind === 'Custom')
		initial_response = (response.data?.filter((item) => (item as OrderJasa).jasa === null) ??
			[]) as OrderJasa[];

	mapped_response = initial_response.map((item) => ({
		kind,
		data:
			kind === 'Custom'
				? { nama: (item as OrderJasa).nama_jasa, harga: item.harga }
				: kind === 'Jasa'
					? (item as OrderJasa).jasa
					: kind === 'Paket'
						? (item as OrderPaket).paket
						: (item as OrderSparepart).sparepart,
		qty: item.kuantitas,
		harga:
			kind === 'Jasa'
				? (item as OrderJasa).jasa.harga
				: kind === 'Paket'
					? (item as OrderPaket).paket.harga
					: kind === 'Sparepart'
						? (item as OrderSparepart).sparepart.harga_jual
						: item.harga,
		montir: item.montir
	})) as Service[];

	return mapped_response;
});
