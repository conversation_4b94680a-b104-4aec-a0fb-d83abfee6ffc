# PDF Export Functionality

This document explains how to use the PDF export functionality in the Montir application.

## Overview

The PDF export feature uses <PERSON><PERSON> to generate PDFs from any route in your application. It provides a server-side remote function that can be called from client-side code to generate and download PDFs.

## Installation

First, install Playwright:

```bash
npm install playwright --legacy-peer-deps
```

Then install the browser binaries:

```bash
npx playwright install webkit
```

## Files Structure

- `src/lib/remote/utility.remote.ts` - Server-side PDF export function
- `src/lib/utils/pdf.ts` - Client-side PDF utilities
- `src/lib/components/PdfExportButton.svelte` - Reusable PDF export button component
- `src/routes/example-pdf-export/+page.svelte` - Example usage page

## Basic Usage

### 1. Using the Remote Function Directly

```typescript
import { exportAsPdf } from '$lib/remote/utility.remote';
import { downloadPdfFromBase64 } from '$lib/utils/pdf';

async function exportCurrentPage() {
  const result = await exportAsPdf({
    route: window.location.href,
    filename: 'my-export.pdf',
    options: {
      format: 'A4',
      landscape: false,
      printBackground: true
    }
  });
  
  if (result.success) {
    downloadPdfFromBase64(result.data, result.filename);
  } else {
    console.error('Export failed:', result.error);
  }
}
```

### 2. Using the PdfExportButton Component

```svelte
<script>
  import PdfExportButton from '$lib/components/PdfExportButton.svelte';
</script>

<!-- Basic usage -->
<PdfExportButton />

<!-- With custom options -->
<PdfExportButton 
  route="/invoice/123"
  filename="invoice-123.pdf"
  options={{
    format: 'A4',
    landscape: true,
    margin: { top: '2cm', right: '2cm', bottom: '2cm', left: '2cm' }
  }}
  variant="primary"
  size="lg"
/>
```

## Configuration Options

### PDF Export Options

```typescript
type PdfExportOptions = {
  format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal';
  landscape?: boolean;
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  printBackground?: boolean;
  scale?: number;
};
```

### Button Component Props

```typescript
interface Props {
  route?: string;           // URL to export (defaults to current page)
  filename?: string;        // PDF filename (auto-generated if not provided)
  options?: PdfExportOptions; // PDF generation options
  variant?: 'primary' | 'secondary' | 'outline'; // Button style
  size?: 'sm' | 'md' | 'lg'; // Button size
  disabled?: boolean;       // Disable the button
  class?: string;          // Additional CSS classes
}
```

## Advanced Usage

### Custom PDF Processing

```typescript
import { exportAsPdf } from '$lib/remote/utility.remote';

async function exportWithCustomProcessing() {
  const result = await exportAsPdf({
    route: '/report/monthly',
    filename: 'monthly-report.pdf',
    options: {
      format: 'A4',
      landscape: true,
      scale: 0.8,
      margin: {
        top: '2cm',
        right: '1.5cm',
        bottom: '2cm',
        left: '1.5cm'
      }
    }
  });
  
  if (result.success) {
    // Instead of downloading, you could:
    
    // 1. Open in new tab
    openPdfInNewTab(result.data);
    
    // 2. Send to server for storage
    await fetch('/api/save-pdf', {
      method: 'POST',
      body: JSON.stringify({
        filename: result.filename,
        data: result.data
      })
    });
    
    // 3. Process the PDF data further
    console.log(`Generated PDF: ${result.filename}, Size: ${result.size} bytes`);
  }
}
```

### Integration with Existing Pages

Add PDF export to any existing page:

```svelte
<!-- In your page component -->
<script>
  import PdfExportButton from '$lib/components/PdfExportButton.svelte';
</script>

<div class="page-header">
  <h1>Invoice #12345</h1>
  <div class="actions">
    <PdfExportButton 
      filename="invoice-12345.pdf"
      options={{ printBackground: true }}
    />
  </div>
</div>

<!-- Your page content -->
<div class="invoice-content">
  <!-- Invoice details -->
</div>
```

## Error Handling

The PDF export function returns a result object that indicates success or failure:

```typescript
type PdfExportResult = {
  success: true;
  filename: string;
  data: string; // base64 encoded PDF
  size: number;
} | {
  success: false;
  error: string;
};
```

Always check the `success` property before processing the result:

```typescript
const result = await exportAsPdf({ route: '/some-page' });

if (result.success) {
  // Handle successful export
  downloadPdfFromBase64(result.data, result.filename);
} else {
  // Handle error
  console.error('PDF export failed:', result.error);
  // Show user-friendly error message
}
```

## Best Practices

1. **Always handle errors** - Network issues, browser crashes, or page load failures can cause exports to fail
2. **Use meaningful filenames** - Include dates, IDs, or other identifying information
3. **Consider page load time** - The function waits for network idle, but complex pages may need additional wait time
4. **Test with your content** - Different page layouts may require different margin and scale settings
5. **Provide user feedback** - Show loading states and success/error messages

## Troubleshooting

### Common Issues

1. **"Cannot find module 'playwright'"** - Make sure Playwright is installed
2. **Browser launch fails** - Install browser binaries with `npx playwright install webkit`
3. **PDF is blank** - Check if the route is accessible and loads properly
4. **Styling issues** - Ensure `printBackground: true` is set to include background colors/images
5. **Content cut off** - Adjust margins or scale options

### Performance Considerations

- Each PDF export launches a new browser instance
- Large pages or complex layouts may take longer to export
- Consider implementing rate limiting for production use
- Browser instances are automatically cleaned up after export

## Example Implementation

See `src/routes/example-pdf-export/+page.svelte` for a complete working example with various export options and error handling.
