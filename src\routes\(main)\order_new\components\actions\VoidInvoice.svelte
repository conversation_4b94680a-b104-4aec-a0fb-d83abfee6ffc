<script lang="ts">
	import type { Order } from '$lib/schema/order';
	import { voidInvoice, getOrder } from '$lib/remote/order.remote';

	import Icon from '@iconify/svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	interface IProps {
		order: Order;
	}
	const { order }: IProps = $props();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: '<PERSON>nfirma<PERSON>',
				description: '<PERSON><PERSON>kah Anda yakin akan membatalkan (void) invoice ini?',
				loader: 'void:invoice:' + order.nomor_invoice
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<form
	{...voidInvoice.enhance(async ({ submit, form }) => {
		await submit().updates(
			getOrder('Selesai').withOverride(
				(orders) => orders?.filter((o) => o.nomor_invoice !== order.nomor_invoice) ?? []
			),
			getOrder('Void').withOverride((orders) => [order, ...(orders ?? [])])
		);
		if (voidInvoice.result?.kind === 'success')
			toastState.add({
				message: 'invoice di-void.',
				type: 'success'
			});

		confirmState.loader = '';
	})}
	class="flex flex-col items-center justify-center p-0"
>
	<input type="hidden" name="nomor_invoice" value={order.nomor_invoice} />
	<button
		class="btn btn-ghost btn-sm btn-error w-full border-b"
		type="button"
		onclick={(e) => confirmation(e)}
	>
		<ConfirmLoader name="void:invoice:{order.nomor_invoice}">
			<Icon icon="mdi:cancel" /> Void
		</ConfirmLoader>
	</button>
</form>
