<script lang="ts">
	import { enhance } from '$app/forms';

	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import FormField from '$lib/utils/formField/FormField.svelte';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `<PERSON><PERSON><PERSON>h Anda yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} spesialisasi ini?`,
				loader: 'action:spesialisasi'
			},
			() => confirmState.submitForm(e)
		);
	};

	const detailState = getDetailState();

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();
</script>

<dialog class="modal" bind:this={detailState.modal}>
	<div class="modal-box text-primary" class:bg-base-200={detailState.mode === 'view'}>
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			<h3 class="text-lg font-bold">Detail Spesialisasi</h3>
			<form method="dialog">
				<button
					class="btn btn-sm btn-circle btn-soft"
					onclick={() => (validationErrorState.errors = {})}>✕</button
				>
			</form>
		</div>

		<FormField
			name="id_spesialisasi"
			label="Kode Spesialisasi (tidak wajib diisi)"
			type="text"
			bind:mode={detailState.mode}
			bind:value={detailState.body.id_spesialisasi}
		/>

		<br />

		<FormField
			name="nama"
			label="Nama Spesialisasi"
			type="text"
			bind:mode={detailState.mode}
			bind:value={detailState.body.nama}
		/>

		<br />

		<form
			action="?/action:spesialisasi"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					detailState.modal?.close();
				})}
		>
			<input type="hidden" name="spesialisasi" value={JSON.stringify(detailState.body)} />
			<input type="hidden" name="mode" value={detailState.mode} />

			<button
				type="button"
				class="btn btn-primary w-full"
				onclick={(e) => confirmation(e)}
				class:hidden={detailState.mode === 'view'}
			>
				<ConfirmLoader name="action:spesialisasi">Simpan</ConfirmLoader>
			</button>
		</form>
	</div>
</dialog>
