import type { Actions, PageServerLoad } from './$types';
import {
	KategoriSparepartSchema,
	type KategoriSparepart,
	type KategoriSparepartEncoded
} from '$lib/schema/general';

import { Effect } from 'effect';
import { effectfulFetch } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ url }) => {
	// const limit = url.searchParams.get('limit') ?? '10';
	// const offset = url.searchParams.get('offset') ?? '1';
	// const keyword = url.searchParams.get('keyword') ?? '';

	const getter = effectfulFetch<KategoriSparepart[]>(`/kategorisparepart`);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success') return { list: [], total_rows: 0 };

	return { list: response.data, total_rows: response.total_rows };
};

export const actions: Actions = {
	'action:kategori-sparepart': async ({ request }) => {
		const data = await request.formData();
		const kategoriSparepart = JSON.parse(
			data.get('kategori_sparepart') as string
		) as KategoriSparepart;
		const mode = data.get('mode');

		const decoded = decodeForm<KategoriSparepart, KategoriSparepartEncoded>(
			KategoriSparepartSchema,
			kategoriSparepart
		);
		if ('error' in decoded) return fail(400, { errors: decoded.errors });

		const action = effectfulFetch('/kategorisparepart', {
			method: mode === 'add' ? 'POST' : 'PUT',
			body: JSON.stringify({
				id_kategori_sparepart: kategoriSparepart.id_kategori_sparepart,
				nama_kategori: kategoriSparepart.nama_kategori
			})
		});

		const response = Effect.runPromise(action);

		return response;
	},
	'delete:kategori-sparepart': async ({ request }) => {
		const data = await request.formData();
		const id = data.get('id');

		const action = effectfulFetch(`/kategorisparepart/${id}`, {
			method: 'DELETE'
		});

		const response = Effect.runPromise(action);
		return response;
	}
};
