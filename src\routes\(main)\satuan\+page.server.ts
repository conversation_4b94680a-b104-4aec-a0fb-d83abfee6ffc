import type { Actions, PageServerLoad } from './$types';
import { SatuanSchema, type Satuan, type SatuanEncoded } from '$lib/schema/general';

import { Effect } from 'effect';
import { effectfulFetch } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ url }) => {
	// const limit = url.searchParams.get('limit') ?? '10';
	// const offset = url.searchParams.get('offset') ?? '1';
	// const keyword = url.searchParams.get('keyword') ?? '';

	const getter = effectfulFetch<Satuan[]>(`/satuan`);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success') return { list: [], total_rows: 0 };

	return { list: response.data, total_rows: response.total_rows };
};

export const actions: Actions = {
	'action:satuan': async ({ request }) => {
		const data = await request.formData();
		const satuan = JSON.parse(data.get('satuan') as string) as Satuan;
		const mode = data.get('mode');

		const decoded = decodeForm<Satuan, SatuanEncoded>(SatuanSchema, satuan);
		if ('error' in decoded) return fail(400, { errors: decoded.errors });

		const action = effectfulFetch('/satuan', {
			method: mode === 'add' ? 'POST' : 'PUT',
			body: JSON.stringify({
				nama_satuan: satuan.nama_satuan
			})
		});

		const response = Effect.runPromise(action);

		return response;
	},
	'delete:satuan': async ({ request }) => {
		const data = await request.formData();
		const id = data.get('id');

		const action = effectfulFetch(`/satuan/${id}`, {
			method: 'DELETE'
		});

		const response = Effect.runPromise(action);
		return response;
	}
};
