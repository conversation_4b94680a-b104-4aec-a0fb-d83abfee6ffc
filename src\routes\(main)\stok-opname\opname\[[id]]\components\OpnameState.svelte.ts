import { _KategoriSparepart, type DetailOpname } from '$lib/schema/general';
import { getContext, setContext } from 'svelte';
import Fuse from 'fuse.js';

export interface OpnameState {
	opnameId: string | undefined;

	category: string;

	sparepart: DetailOpname[];
	filtered: DetailOpname[];

	keyword: string;

	date: string;

	auditCount: number;
	showAudited: boolean;
}

export default class OpnameStateClass implements OpnameState {
	opnameId = $state<string | undefined>(undefined);

	category = $state<string>('');
	keyword = $state<string>('');
	date = $state<string>(new Date().toISOString().split('T')[0]);

	sparepart = $state<DetailOpname[]>([]);

	auditCount = $derived(
		this.sparepart.filter((item) => item.stok_fisik !== item.stok_sistem).length
	);

	showAudited = $state<boolean>(false);

	filtered = $derived.by<DetailOpname[]>(() => {
		const category_filtered = this.sparepart.filter((item) =>
			this.category === ''
				? true
				: item.kartustok.sparepart.id_kategori_sparepart?.id_kategori_sparepart === this.category
		);

		const audit_filtered = !this.showAudited
			? category_filtered
			: category_filtered.filter((item) => item.stok_fisik !== item.stok_sistem);

		if (this.keyword === '') return audit_filtered;

		const fuse = new Fuse(audit_filtered, {
			keys: ['kartustok.sparepart.nama_sparepart']
		});

		return fuse.search(this.keyword).map((item) => item.item);
	});
}

const OPNAME_STATE_KEY = Symbol('@@opname-state@@');

export function setOpnameState(): void {
	setContext(OPNAME_STATE_KEY, new OpnameStateClass());
}

export function getOpnameState(): OpnameState {
	return getContext<OpnameState>(OPNAME_STATE_KEY);
}
