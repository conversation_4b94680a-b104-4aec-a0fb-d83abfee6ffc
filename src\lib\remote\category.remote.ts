import { query } from '$app/server';

import type { KategoriSparepart } from '$lib/schema/general';

import { Effect } from 'effect';
import { effectfulFetch } from '$lib/utils/fetch';

export const getSparepartCategory = query(async () => {
	const getCategory = effectfulFetch<KategoriSparepart[]>('/kategorisparepart');
	const response = await Effect.runPromise(getCategory);

	if (response.kind !== 'success') return { data: [], total_rows: 0 };
	else return response;
});
